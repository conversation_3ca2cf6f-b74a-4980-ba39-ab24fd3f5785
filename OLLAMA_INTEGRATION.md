# Ollama Integration for Fillify

## Overview
This document describes the integration of Ollama as a new AI service provider in the Fillify browser extension. Ollama allows users to run large language models locally on their machine, providing privacy and offline capabilities.

## Features Added

### 1. Direct Local Processing
- **File**: `entrypoints/background.ts`
- **Function**: `handleOllamaRequest()`
- **Features**:
  - Bypasses backend server for Ollama requests
  - Direct connection to local Ollama server
  - Custom prompt building for form filling
  - Response parsing and formatting

### 2. Ollama Service Class
- **File**: `src/services/ai-service.ts`
- **Class**: `OllamaService`
- **Features**:
  - Extends the abstract `AIService` class
  - Uses OpenAI-compatible API endpoints (`/v1/chat/completions`)
  - Configurable endpoint (default: `http://localhost:11434`)
  - Supports all Ollama models

### 2. Model Fetching
- **Function**: `fetchOllamaModels(endpoint)`
- **Features**:
  - Fetches available models from Ollama server
  - Uses `/v1/models` endpoint
  - All models marked as free (local execution)
  - Fallback to default model list if API fails

### 3. Provider Configuration
- **Files Updated**:
  - `entrypoints/background.ts`
  - `entrypoints/settings/constants.ts`
  - `entrypoints/onboarding/App.vue`
  - `entrypoints/settings/App.vue`

### 4. UI Integration
- **Onboarding**: Added Ollama as provider option
- **Settings**: Added Ollama provider card
- **Input Fields**: Shows "Enter Ollama endpoint" instead of "API key"
- **Help Links**: Points to Ollama download page

### 5. Connection Validation
- **Method**: Tests connection to Ollama server
- **Endpoint**: Uses `/v1/models` for validation
- **Storage**: Saves endpoint URL instead of API key

## Configuration

### Default Settings
- **Default Endpoint**: `http://localhost:11434`
- **Default Model**: `llama2`
- **API Key**: `ollama` (required by API but unused)

### Storage Keys
- **Endpoint**: Stored in `settings.ollama_endpoint`
- **Validation**: Stored in `validatedKeys.ollama`

## Model Name Formatting
Added support for common Ollama model naming patterns:
- `llama2` → `Llama 2`
- `llama2:13b` → `Llama 2 13B`
- `mistral` → `Mistral`
- `codellama` → `Code Llama`
- `phi` → `Phi`

## Usage Instructions

### For Users
1. **Install Ollama**: Download from https://ollama.com/download
2. **Pull Models**: Run `ollama pull llama2` in terminal
3. **Start Server**: Ollama runs automatically on port 11434
4. **Configure Extension**:
   - Open Fillify settings
   - Select "Ollama" as provider
   - Enter endpoint (default: `http://localhost:11434`)
   - Validate connection

### For Developers
The integration follows the same pattern as other providers:
```typescript
// Create service
const service = new OllamaService('ollama', 'llama2', 'http://localhost:11434');

// Generate content
const result = await service.generateContent('Hello, world!');
```

## Files Modified

### Core Service Files
- `src/services/ai-service.ts` - Added OllamaService class and fetchOllamaModels function
- `src/types/index.ts` - Added ollama to ApiKeys interface

### Background Script
- `entrypoints/background.ts` - Added Ollama provider support and validation

### Settings Page
- `entrypoints/settings/App.vue` - Added Ollama provider UI and logic
- `entrypoints/settings/constants.ts` - Added Ollama models configuration

### Onboarding Page
- `entrypoints/onboarding/App.vue` - Added Ollama provider option

## Benefits

### Privacy
- Models run locally on user's machine
- **No data sent to external servers** (including your backend)
- Complete offline functionality
- Direct browser-to-Ollama communication

### Cost
- No API costs after initial setup
- Unlimited usage once models are downloaded
- No backend processing costs

### Performance
- Low latency for local inference
- No network dependency during usage
- Direct connection eliminates proxy overhead

### Flexibility
- Support for any Ollama-compatible model
- Easy model switching and management
- Independent of backend service availability

## Technical Notes

### Architecture Change
**Important**: Ollama requests bypass your backend server entirely:
- Other providers: Browser → Your Backend → AI Provider API
- Ollama: Browser → Local Ollama Server (direct)

This means:
- No backend modifications needed for Ollama support
- Ollama requests don't appear in your backend logs
- No server-side prompt processing for Ollama
- Complete client-side form filling logic

### CORS Solution
To solve cross-origin issues when accessing local Ollama server:

1. **declarativeNetRequest API**: Automatically removes Origin and Referer headers
2. **Host Permissions**: Added localhost and 127.0.0.1 permissions
3. **Request Headers**: Optimized headers for Ollama compatibility

**Files Added/Modified for CORS**:
- `public/rules.json` - declarativeNetRequest rules
- `wxt.config.ts` - Added permissions and rule configuration
- `manifest.json` - Generated with CORS permissions

### API Compatibility
Ollama provides OpenAI-compatible endpoints, making integration straightforward:
- Chat completions: `/v1/chat/completions`
- Model listing: `/v1/models`

### Prompt Building
The extension builds form-filling prompts locally:
- Bug report mode: Structured JSON response format
- Email mode: Subject/body format
- General mode: Flexible field mapping

### Error Handling
- Connection validation before usage
- Graceful fallback to default models
- Clear error messages for setup issues

### Future Enhancements
- Model download management
- Performance monitoring
- Advanced configuration options
