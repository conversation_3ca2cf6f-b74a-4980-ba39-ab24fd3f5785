document.addEventListener('DOMContentLoaded', function() {
    const steps = document.querySelectorAll('.step');
    const dots = document.querySelectorAll('.dot');
    const nextButton = document.querySelector('.next-button');
    const backButton = document.querySelector('.back-button');
    const providerSelect = document.getElementById('providerSelect');
    const apiKeyInput = document.getElementById('apiKeyInput');
    const apiKeyInputWrapper = document.getElementById('apiKeyInputWrapper');
    const setupLaterBtn = document.getElementById('setupLaterBtn');
    const getApiKeyLink = document.getElementById('getApiKeyLink');
    const toggleVisibility = document.getElementById('toggleVisibility');

    let currentStep = 1;
    const totalSteps = steps.length;

    // API Key 获取链接
    const providerLinks = {
        openai: 'https://platform.openai.com/api-keys',
        moonshot: 'https://platform.moonshot.cn/console/api-keys',
        claude: 'https://console.anthropic.com/account/keys',
        gemini: 'https://makersuite.google.com/app/apikey',
        deepseek: 'https://platform.deepseek.com/api_keys',
        openrouter: 'https://openrouter.ai/keys'
    };

    // Constants
    const STORAGE_KEYS = {
        API_KEYS: 'formify_api_keys',
        SETTINGS: 'formify_settings',
        ONBOARDING_COMPLETED: 'formify_onboarding_completed',
        VALIDATED_KEYS: 'formify_validated_keys'
    };

    // 更新步骤显示
    function updateStep() {
        steps.forEach(step => step.classList.remove('active'));
        dots.forEach(dot => dot.classList.remove('active'));

        document.querySelector(`[data-step="${currentStep}"]`).classList.add('active');
        dots[currentStep - 1].classList.add('active');

        // 更新导航按钮
        backButton.style.display = currentStep === 1 ? 'none' : 'block';
        nextButton.textContent = currentStep === totalSteps ? 'Finish' : 'Next';
    }

    // 处理密码可见性切换
    toggleVisibility.addEventListener('click', () => {
        const type = apiKeyInput.type === 'password' ? 'text' : 'password';
        apiKeyInput.type = type;

        // 更新图标
        const path = toggleVisibility.querySelector('path');
        if (type === 'text') {
            path.setAttribute('d', 'M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z');
        } else {
            path.setAttribute('d', 'M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7zM2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3 2 4.27zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2zm4.31-.78l3.15 3.15.02-.16c0-1.66-1.34-3-3-3l-.17.01z');
        }
    });

    // 处理服务商选择
    providerSelect.addEventListener('change', function() {
        const provider = this.value;
        if (provider) {
            apiKeyInputWrapper.style.display = 'block';
            getApiKeyLink.href = providerLinks[provider] || '#';
            apiKeyInput.placeholder = `Enter your ${provider} API key`;
        } else {
            apiKeyInputWrapper.style.display = 'none';
        }
    });

    // 验证 API Key
    function validateApiKey(apiKey, provider) {
        if (!apiKey) return false;

        switch (provider) {
            case 'openai':
                return apiKey.startsWith('sk-') && apiKey.length > 20;
            case 'moonshot':
                return apiKey.length > 20;
            case 'claude':
                return apiKey.startsWith('sk-') && apiKey.length > 20;
            case 'gemini':
                return apiKey.length > 20;
            case 'deepseek':
                return apiKey.startsWith('sk-') && apiKey.length > 20;
            case 'openrouter':
                return apiKey.startsWith('sk-') && apiKey.length > 20;
            default:
                return false;
        }
    }

    // 测试 API key
    async function testApiKey(provider, key) {
        const endpoints = {
            openai: 'https://api.openai.com/v1/models',
            claude: 'https://api.anthropic.com/v1/models',
            moonshot: 'https://api.moonshot.cn/v1/models',
            gemini: `https://generativelanguage.googleapis.com/v1/models?key=${key}`,
            deepseek: 'https://api.deepseek.com/v1/models',
            openrouter: 'https://openrouter.ai/api/v1/models'
        };

        try {
            const headers = {
                'Content-Type': 'application/json'
            };

            if (provider === 'claude') {
                headers['x-api-key'] = key;
                headers['anthropic-version'] = '2023-06-01';
            } else if (provider === 'gemini') {
                // Gemini 不需要额外的认证头，key 已经在 URL 中
            } else if (provider === 'openrouter') {
                headers['Authorization'] = `Bearer ${key}`;
                headers['HTTP-Referer'] = 'https://fillify.tech';
                headers['X-Title'] = 'Fillify';
            } else {
                headers['Authorization'] = `Bearer ${key}`;
            }

            const response = await fetch(endpoints[provider], {
                method: 'GET',
                headers: headers
            });

            return response.ok;
        } catch (error) {
            console.error(`Error testing ${provider} API key:`, error);
            return false;
        }
    }

    // 保存 API Key
    async function saveApiKey(apiKey, provider) {
        try {
            // 首先测试 API key
            const isValid = await testApiKey(provider, apiKey);
            if (!isValid) {
                alert(`Invalid ${provider.toUpperCase()} API key. Please check and try again.`);
                return false;
            }

            // 获取现有设置
            const storage = await chrome.storage.sync.get([
                STORAGE_KEYS.SETTINGS,
                STORAGE_KEYS.API_KEYS,
                STORAGE_KEYS.VALIDATED_KEYS
            ]);
            const settings = storage[STORAGE_KEYS.SETTINGS] || {};
            const apiKeys = storage[STORAGE_KEYS.API_KEYS] || {};
            const validatedKeys = storage[STORAGE_KEYS.VALIDATED_KEYS] || {};

            // 更新设置
            settings.defaultProvider = provider;

            // 根据提供商设置模型
            switch (provider) {
                case 'openai':
                    settings.defaultModel = 'gpt-3.5-turbo';
                    break;
                case 'moonshot':
                    settings.defaultModel = 'moonshot-v1-8k';
                    break;
                case 'claude':
                    settings.defaultModel = 'claude-3-sonnet-20240229';
                    break;
                case 'gemini':
                    settings.defaultModel = 'gemini-2.0-flash';
                    break;
                case 'deepseek':
                    settings.defaultModel = 'deepseek-chat';
                    break;
                case 'openrouter':
                    settings.defaultModel = 'openai/gpt-3.5-turbo';
                    break;
            }

            // 保存 API Key 和验证状态
            apiKeys[provider] = apiKey;
            validatedKeys[provider] = apiKey;

            await chrome.storage.sync.set({
                [STORAGE_KEYS.API_KEYS]: apiKeys,
                [STORAGE_KEYS.SETTINGS]: settings,
                [STORAGE_KEYS.VALIDATED_KEYS]: validatedKeys
            });

            return true;
        } catch (error) {
            console.error('Error saving API key:', error);
            return false;
        }
    }

    // 处理"稍后设置"按钮点击
    setupLaterBtn.addEventListener('click', () => {
        currentStep++;
        updateStep();
    });

    // 处理下一步按钮点击
    nextButton.addEventListener('click', async () => {
        // 如果在 API Key 输入步骤
        if (currentStep === 2) {
            const provider = providerSelect.value;
            const apiKey = apiKeyInput.value.trim();

            // 如果没有选择提供商
            if (!provider) {
                alert('Please select an AI provider');
                return;
            }

            // 如果选择了提供商但没有点击"Set up later"
            if (!apiKey) {
                alert('Please enter your API key or click "Set up later"');
                return;
            }

            // 验证 API Key 格式
            if (!validateApiKey(apiKey, provider)) {
                alert('Please enter a valid API key');
                return;
            }

            // 保存 API Key
            const saved = await saveApiKey(apiKey, provider);
            if (!saved) {
                alert('Failed to save API key. Please try again.');
                return;
            }
        }

        // 如果是最后一步，完成设置
        if (currentStep === totalSteps) {
            // 标记引导已完成
            await chrome.storage.sync.set({
                [STORAGE_KEYS.ONBOARDING_COMPLETED]: true
            });

            // 关闭当前标签页
            window.close();
            return;
        }

        currentStep++;
        updateStep();
    });

    // 处理返回按钮点击
    backButton.addEventListener('click', () => {
        if (currentStep > 1) {
            currentStep--;
            updateStep();
        }
    });

    // 初始化显示
    updateStep();
});