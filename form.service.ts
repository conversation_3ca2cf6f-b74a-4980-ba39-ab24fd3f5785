import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { FormRequest, ProjectInfo, AIProviderConfig, AIResponse, OpenAIResponse, MoonshotResponse, ClaudeResponse, GeminiResponse, OpenRouterResponse } from '../../../common/types/form.types';
import { AppError } from '../../../common/utils/error';
import fetch from 'node-fetch';
import { ConfigService } from '@nestjs/config';
import { Inject } from '@nestjs/common';
import { SupabaseClient } from '@supabase/supabase-js';

abstract class AIProvider {
  constructor(protected config: AIProviderConfig) {}
  abstract generateContent(prompt: string): Promise<AIResponse>;
}

class OpenAIProvider extends AIProvider {
  async generateContent(prompt: string): Promise<AIResponse> {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify({
        model: this.config.model || 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a helpful assistant that generates form content based on descriptions. Always return valid JSON.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7
      })
    });

    const data = await response.json() as OpenAIResponse;

    if (!response.ok) {
      throw new AppError(response.status, data.error?.message || 'OpenAI API error');
    }

    return {
      content: data.choices[0].message.content,
      usage: data.usage
    };
  }
}

class MoonshotProvider extends AIProvider {
  async generateContent(prompt: string): Promise<AIResponse> {
    const response = await fetch('https://api.moonshot.cn/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify({
        model: this.config.model || 'moonshot-v1-8k',
        messages: [
          {
            role: 'system',
            content: 'You are a helpful assistant that generates form content based on descriptions. Always return valid JSON.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7
      })
    });

    const data = await response.json() as MoonshotResponse;

    if (!response.ok) {
      throw new AppError(response.status, data.error?.message || 'Moonshot API error');
    }

    return {
      content: data.choices[0].message.content,
      usage: data.usage
    };
  }
}

class ClaudeProvider extends AIProvider {
  async generateContent(prompt: string): Promise<AIResponse> {
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': this.config.apiKey,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model: this.config.model || 'claude-3-haiku-20240307',
        max_tokens: 1000,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        system: 'You are a helpful assistant that generates form content based on descriptions. Always return valid JSON.'
      })
    });

    const data = await response.json() as ClaudeResponse;

    if (!response.ok) {
      throw new AppError(response.status, data.error?.message || 'Claude API error');
    }

    // Extract text content from the response
    let textContent = '';
    if (data.content && Array.isArray(data.content)) {
      for (const contentItem of data.content) {
        if (contentItem.type === 'text') {
          textContent += contentItem.text;
        }
      }
    }

    return {
      content: textContent,
      usage: {
        prompt_tokens: data.usage?.input_tokens || 0,
        completion_tokens: data.usage?.output_tokens || 0,
        total_tokens: (data.usage?.input_tokens || 0) + (data.usage?.output_tokens || 0)
      }
    };
  }
}

class GeminiProvider extends AIProvider {
  async generateContent(prompt: string): Promise<AIResponse> {
    const apiKey = this.config.apiKey;
    const model = this.config.model || 'gemini-2.0-flash';
    const response = await fetch(`https://generativelanguage.googleapis.com/v1/models/${model}:generateContent?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        contents: [
          {
            role: "user",
            parts: [
              {
                text: `You are a helpful assistant that generates form content based on descriptions. Always return valid JSON object without any markdown formatting or additional text. Here's the task:\n\n${prompt}`
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.7,
          topP: 0.8,
          topK: 40
        }
      })
    });

    const data = await response.json() as GeminiResponse;

    if (!response.ok) {
      throw new AppError(response.status, data.error?.message || 'Gemini API error');
    }

    if (!data.candidates?.[0]?.content?.parts?.[0]?.text) {
      throw new AppError(HttpStatus.BAD_REQUEST, 'Invalid response from Gemini API');
    }

    const rawContent = data.candidates[0].content.parts[0].text;

    // 尝试清理和解析内容
    try {
      // 移除可能的 markdown 标记
      let cleanContent = rawContent.replace(/^```json\s*/, '').replace(/```\s*$/, '');

      // 移除可能的注释
      cleanContent = cleanContent.replace(/\/\*[\s\S]*?\*\/|\/\/.*/g, '');

      // 尝试解析确保是有效的 JSON
      const parsedContent = JSON.parse(cleanContent);

      return {
        content: JSON.stringify(parsedContent),
        usage: {
          prompt_tokens: data.usageMetadata?.promptTokenCount || data.promptFeedback?.tokenCount?.promptTokens || 0,
          completion_tokens: data.usageMetadata?.candidatesTokenCount || 0,
          total_tokens: data.usageMetadata?.totalTokenCount || data.promptFeedback?.tokenCount?.totalTokens || 0
        }
      };
    } catch (error) {
      console.error('Gemini raw response:', rawContent);
      throw new AppError(HttpStatus.BAD_REQUEST, 'Failed to parse Gemini response as JSON');
    }
  }
}

class DeepseekProvider extends AIProvider {
  async generateContent(prompt: string): Promise<AIResponse> {
    const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify({
        model: this.config.model || 'deepseek-chat',
        messages: [
          {
            role: 'system',
            content: 'You are a helpful assistant that generates form content based on descriptions. Always return valid JSON.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7
      })
    });

    const data = await response.json();

    if (!response.ok) {
      throw new AppError(response.status, data.error?.message || 'Deepseek API error');
    }

    return {
      content: data.choices[0].message.content,
      usage: data.usage
    };
  }
}

class OpenRouterProvider extends AIProvider {
  async generateContent(prompt: string): Promise<AIResponse> {
    // 默认使用 OpenAI 的 GPT-3.5 Turbo 模型，但可以通过 config.model 指定其他模型
    // OpenRouter 支持多种模型，格式为 "provider/model"，例如：
    // - openai/gpt-4o
    // - anthropic/claude-3-opus
    // - google/gemini-pro
    // - meta-llama/llama-3-70b-instruct
    const model = this.config.model || 'openai/gpt-3.5-turbo';

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`,
        'HTTP-Referer': 'https://fillify.tech', // 可以替换为你的网站 URL
        'X-Title': 'Fillify' // 可以替换为你的应用名称
      },
      body: JSON.stringify({
        model: model,
        messages: [
          {
            role: 'system',
            content: 'You are a helpful assistant that generates form content based on descriptions. Always return valid JSON without any markdown formatting or additional text.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 1000,
        response_format: { type: 'json_object' } // 请求 JSON 格式的响应
      })
    });

    const data = await response.json() as OpenRouterResponse;

    if (!response.ok) {
      const errorMessage = data.choices?.[0]?.error?.message || 'OpenRouter API error';
      throw new AppError(response.status, errorMessage);
    }

    if (!data.choices || data.choices.length === 0 || !data.choices[0].message.content) {
      throw new AppError(HttpStatus.BAD_REQUEST, 'Invalid response from OpenRouter API');
    }

    const rawContent = data.choices[0].message.content;

    // 尝试清理和解析内容
    try {
      // 移除可能的 markdown 标记
      let cleanContent = rawContent.replace(/^```json\s*/, '').replace(/```\s*$/, '');

      // 移除可能的注释
      cleanContent = cleanContent.replace(/\/\*[\s\S]*?\*\/|\/\/.*/g, '');

      // 尝试解析确保是有效的 JSON
      const parsedContent = JSON.parse(cleanContent);

      return {
        content: JSON.stringify(parsedContent),
        usage: {
          prompt_tokens: data.usage.prompt_tokens,
          completion_tokens: data.usage.completion_tokens,
          total_tokens: data.usage.total_tokens
        }
      };
    } catch (error) {
      console.error('OpenRouter raw response:', rawContent);
      throw new AppError(HttpStatus.BAD_REQUEST, 'Failed to parse OpenRouter response as JSON');
    }
  }
}

@Injectable()
export class FormService {
  constructor(
    private configService: ConfigService,
    @Inject('SUPABASE')
    private supabase: SupabaseClient
  ) {}

  // 添加语言代码映射
  private readonly languageCodeMap: { [key: string]: string } = {
    'id': 'Bahasa Indonesia',
    'ms': 'Bahasa Melayu',
    'da': 'Dansk',
    'de': 'Deutsch',
    'en': 'English',
    'es': 'Español',
    'fr': 'Français',
    'it': 'Italiano',
    'nl': 'Nederlands',
    'no': 'Norsk',
    'pl': 'Polski',
    'pt': 'Português',
    'ro': 'Română',
    'fi': 'Suomi',
    'sv': 'Svenska',
    'vi': 'Tiếng Việt',
    'tr': 'Türkçe',
    'hu': 'Magyar',
    'cs': 'Čeština',
    'uk': 'Українська',
    'ru': 'Русский',
    'bg': 'Български',
    'ar': 'العربية',
    'fa': 'فارسی',
    'he': 'עִבְרִית',
    'hi': 'हिन्दी',
    'th': 'ไทย',
    'ja': '日本語',
    'zh-CN': '中文（简体）',
    'zh-TW': '中文（繁體）',
    'ko': '한국어'
  };

  private getLanguageName(languageCode: string): string {
    return this.languageCodeMap[languageCode] || 'English';
  }

  private generateLanguageInstruction(request: FormRequest): string {
    if (request.language === 'auto') {
      return `First detect the language of this text: "${request.description}". Then generate ALL content using EXACTLY the same language as the detected language. For example, if the text is in Chinese, generate all content in Chinese; if the text is in English, generate all content in English, etc.`;
    }

    const languageName = this.getLanguageName(request.language);
    return `Generate all content in ${languageName}`;
  }

  private cleanAIResponse(content: string): string {
    try {
      content = content.replace(/^```json\n?/, '').replace(/\n?```$/, '');

      const parsed = JSON.parse(content);
      return JSON.stringify(parsed);
    } catch (error) {
      throw new AppError(HttpStatus.BAD_REQUEST, 'Invalid JSON response from AI provider');
    }
  }

  private getProvider(provider: string, useCustomApi: boolean, customApiKey?: string, model?: string): AIProvider {
    // 如果不使用自定义API，使用环境变量中的API key
    const apiKey = useCustomApi ? customApiKey : this.configService.get<string>('DEEPSEEK_API_KEY');

    if (!apiKey) {
      throw new AppError(HttpStatus.BAD_REQUEST, 'API key is not configured');
    }

    switch (provider?.toLowerCase() || 'deepseek') {
      case 'openai':
        return new OpenAIProvider({ apiKey, model });
      case 'moonshot':
        return new MoonshotProvider({ apiKey, model });
      case 'claude':
        return new ClaudeProvider({ apiKey, model });
      case 'gemini':
        // 如果没有指定模型，使用默认的 gemini-2.0-flash 模型
        return new GeminiProvider({ apiKey, model: model || 'gemini-2.0-flash' });
      case 'openrouter':
        // OpenRouter 支持多种模型，格式为 "provider/model"，例如 openai/gpt-4o
        return new OpenRouterProvider({ apiKey, model });
      case 'deepseek':
      default:
        return new DeepseekProvider({ apiKey, model });
    }
  }

  private generateBugReportPromptWithProject(request: FormRequest): string {
    // 只在非 auto 的情况下获取语言名称
    const language = request.language !== 'auto' ? this.getLanguageName(request.language) : null;
    const languageInstruction = request.language === 'auto'
      ? `First detect the language of this text: "${request.description}". Then generate ALL content using EXACTLY the same language as the detected language. For example, if the text is in Chinese, generate all content in Chinese; if the text is in English, generate all content in English, etc.`
      : `Generate all content in ${language}`;

    const projectContext = `Project Information:
- Project Name: ${request.project.name || 'N/A'}
- Project Description: ${request.project.description || 'N/A'}
- Environment Details:
${request.project.environment || 'N/A'}

IMPORTANT: You MUST use these exact environment details in your response. Do not modify or omit any environment information.

Project Context:
1. This is a ${request.project.description} named "${request.project.name}"
2. The environment information above MUST be included exactly as shown in the environment section of your response
3. All generated content should be relevant to this specific project
`;

    return `As an experienced QA engineer specializing in bug reporting, analyze the provided bug description and generate structured content for a bug tracking form in JSON format. ${languageInstruction}.

Context:
${projectContext}
Bug Description: "${request.description}"

Form Fields (Field Names and Types):
${JSON.stringify(request.formFields, null, 2)}

Requirements:
1. ${languageInstruction}
2. Return a valid JSON object where keys match the field names/ids provided in the "Form Fields".
3. CRITICAL - Content Structure:
   - The description field should ONLY contain the template structure below
   - Do NOT write any description text before the template
   - Put ALL description content INSIDE the template sections
   - Follow the template structure EXACTLY

4. Field-specific instructions:
   - **Title fields**: Generate a concise and descriptive bug title that includes the project name
   - **Description fields**:
     - Put the COMPLETE bug description ONLY in the template's sections
     - Do not write any description text outside the template structure
     - Include all details (description, expected/actual behavior, impact) within the template sections
   - **Steps fields**: List reproducible steps in a numbered format
   - **Environment fields**: Use EXACTLY the environment information provided in project context
   - **Priority/Severity fields**: Assign a level based on the provided impact assessment
   - **Status fields**: Default to "New" or "Open"

5. Template Structure:
${request.project.template || `### Environment
${request.project.environment}

### Description
Write a detailed description of the bug here, including:
- Detailed bug description
- Impact assessment and severity level

### Steps to Reproduce
1. First specific step
2. Second specific step
3. Additional steps as needed

### Expected behavior
Describe what should happen in ${request.project.name}

### Actual behavior
Describe what actually happens

### Reproduces how often
Specify how frequently the issue occurs

### Additional Information
Add any other relevant context about ${request.project.name}`}

6. Output Requirements:
   - JSON object only (no extra text)
   - Match field types (e.g., string, number) in "Form Fields"
   - Include project-specific details where relevant
   - Use clear and professional language in ${request.language || 'English'}
   - Ensure sensitive data is anonymized
   - DO NOT write any content outside the template structure
   - Put ALL description content INSIDE the template sections

Return only the JSON object. Do not include any additional text or explanation.`;
  }

  private generateBugReportPromptWithoutProject(request: FormRequest): string {
    // 只在非 auto 的情况下获取语言名称
    const language = request.language !== 'auto' ? this.getLanguageName(request.language) : null;
    const languageInstruction = request.language === 'auto'
      ? `First detect the language of this text: "${request.description}". Then generate ALL content using EXACTLY the same language as the detected language. For example, if the text is in Chinese, generate all content in Chinese; if the text is in English, generate all content in English, etc.`
      : `Generate all content in ${language}`;

    return `As an experienced QA engineer specializing in bug reporting, analyze the provided bug description and generate structured content for a bug tracking form in JSON format. ${languageInstruction}.

Bug Description: "${request.description}"

Form Fields (Field Names and Types):
${JSON.stringify(request.formFields, null, 2)}

Requirements:
1. ${languageInstruction}
2. Return a valid JSON object where keys match the field names/ids provided in the "Form Fields".
3. Field-specific instructions:
   - **Title fields**: Generate a concise and descriptive bug title
   - **Description fields**:
     - Put the COMPLETE bug description ONLY in the template's "Description" section
     - Do not write any description text outside the template structure
     - Include all details (description, expected/actual behavior, impact) within the template sections
   - **Steps fields**: List reproducible steps in a numbered format
   - **Environment fields**: Extract relevant technical details from the description
   - **Priority/Severity fields**: Assign a level based on the provided impact assessment
   - **Status fields**: Default to "New" or "Open"

4. CRITICAL - Content Structure:
   - The description field should ONLY contain the template structure below
   - Do NOT write any description text before the template
   - Put ALL description content INSIDE the template sections
   - Follow the template structure EXACTLY

5. Template Structure:

### Environment
- Version:
- Operating System:
- Browser: Any/Chrome/Safari/Firefox/Edge/Safari for iOS/Chrome for Android/...
- Operating System: Any/Windows/macOS/Linux/ChromeOS/...

### Description
Write a detailed description of the bug here, including:
- Detailed bug description
- Impact assessment and severity level

### Steps to Reproduce
1. First specific step
2. Second specific step
3. Additional steps as needed

### Expected behavior
Describe what you expect to happen

### Actual behavior
Describe what actually happens

### Reproduces how often
Specify how frequently the issue occurs

### Additional Information
Add any other relevant context or information

6. Output Requirements:
   - JSON object only (no extra text)
   - Match field types (e.g., string, number) in "Form Fields"
   - Keep generated content concise but informative
   - Use clear and professional language in ${request.language || 'English'}
   - Ensure sensitive data is anonymized
   - DO NOT write any content outside the template structure

Return only the JSON object. Do not include any additional text or explanation.`;
  }

  private generatePrompt(request: FormRequest): string {
    // 只在非 auto 的情况下获取语言名称
    const language = request.language !== 'auto' ? this.getLanguageName(request.language) : null;

    if (request.mode === 'bugReport') {
      return request.project
        ? this.generateBugReportPromptWithProject(request)
        : this.generateBugReportPromptWithoutProject(request);
    } else if (request.mode === 'email') {
      return `As a professional email writing assistant, generate an email based on the user's description. ${request.language === 'auto'
        ? `First detect the language of this text: "${request.description}". Then generate ALL content using EXACTLY the same language as the detected language.`
        : `Generate all content in ${language}`}.

User Description: "${request.description}"

Form Fields:
${JSON.stringify(request.formFields, null, 2)}

Requirements:
1. Return a JSON object where keys match the field names/ids
2. Generate professional and appropriate email content
3. Keep the tone professional and courteous
4. Be concise but comprehensive

Return only the JSON object with no additional text.`;
    } else {
      return `As a form filling assistant, generate appropriate content for this form based on the user's description. ${request.language === 'auto'
        ? `First detect the language of this text: "${request.description}". Then generate ALL content using EXACTLY the same language as the detected language.`
        : `Generate all content in ${language}`}.

User Description: "${request.description}"

Form Fields:
${JSON.stringify(request.formFields, null, 2)}

Requirements:
1. Return a JSON object where keys match the field names/ids
2. Generate natural, context-appropriate content for each field
3. Respect the field types (text, email, number, etc.)
4. Keep generated content concise but informative

Return only the JSON object with no additional text.`;
    }
  }

  async updateUserCredits(userId: string, tokensUsed: number): Promise<void> {
    console.log('Updating credits for user:', userId);
    console.log('Tokens used:', tokensUsed);

    const creditsToDeduct = tokensUsed / 1000;
    console.log('Credits to deduct:', creditsToDeduct);

    // 使用事务来确保积分检查和更新的原子性
    const { data: userData, error: fetchError } = await this.supabase.rpc('deduct_user_credits', {
      p_user_id: userId,
      p_credits_to_deduct: creditsToDeduct
    });

    if (fetchError) {
      console.error('Error updating user credits:', fetchError);
      if (fetchError.message.includes('insufficient_credits')) {
        throw new AppError(HttpStatus.PAYMENT_REQUIRED, 'Insufficient credits');
      }
      if (fetchError.message.includes('total_credits_exceeded')) {
        throw new AppError(HttpStatus.PAYMENT_REQUIRED, 'You have exceeded your total available credits. Please purchase more credits to continue.');
      }
      throw new AppError(HttpStatus.INTERNAL_SERVER_ERROR, `Failed to update user credits: ${fetchError.message}`);
    }

    console.log('Successfully updated credits, new balance:', userData.new_credits);

    try {
      // 添加交易记录
      const { error: transactionError } = await this.supabase
        .from('credit_transactions')
        .insert({
          user_id: userId,
          amount: -creditsToDeduct,  // 负数表示消费
          type: 'usage',
          description: `Used ${tokensUsed} tokens`
        });

      if (transactionError) {
        console.error('Error creating credit transaction:', transactionError);
        console.error('Transaction details:', {
          user_id: userId,
          amount: -creditsToDeduct,
          type: 'usage',
          description: `Used ${tokensUsed} tokens`
        });
        throw new AppError(HttpStatus.INTERNAL_SERVER_ERROR, `Failed to create credit transaction: ${transactionError.message}`);
      }
    } catch (error) {
      console.error('Error in updateUserCredits:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(HttpStatus.INTERNAL_SERVER_ERROR, `Failed to update user credits: ${error.message}`);
    }
  }

  private estimateTokens(description: string, formFields: string[], mode: string): number {
    // 基础 token 数（包含系统提示词）
    let baseTokens = 100;

    // 根据描述长度估算
    const descriptionTokens = Math.ceil(description.length / 4);

    // 根据表单字段数量估算
    const formFieldsTokens = formFields.length * 20;

    // bug report 模式会生成更多内容
    const modeMultiplier = mode === 'bugReport' ? 2 : 1;

    // 计算总预估 tokens
    const estimatedTokens = (baseTokens + descriptionTokens + formFieldsTokens) * modeMultiplier;

    // 为了安全起见，增加 20% 的缓冲
    return Math.ceil(estimatedTokens * 1.2);
  }

  async generateFormContent(body: FormRequest): Promise<any> {
    try {
      const provider = this.getProvider(
        body.provider,
        body.useCustomApi || false,
        body.apiKey,
        body.model
      );

      // 如果使用系统API，需要预先检查积分是否足够
      if (!body.useCustomApi && body.userId) {
        // 预估本次请求需要的 tokens
        const estimatedTokens = this.estimateTokens(body.description, body.formFields, body.mode);
        const estimatedCredits = estimatedTokens / 1000;

        // 检查用户是否有足够的积分
        const { data: userData, error: checkError } = await this.supabase
          .from('users')
          .select('credits')
          .eq('id', body.userId)
          .single();

        if (checkError) {
          throw new AppError(HttpStatus.INTERNAL_SERVER_ERROR, `Failed to check user credits: ${checkError.message}`);
        }

        if (!userData || userData.credits < estimatedCredits) {
          throw new AppError(HttpStatus.PAYMENT_REQUIRED, `Insufficient credits. Required: ${estimatedCredits.toFixed(3)}, Available: ${userData.credits.toFixed(3)}`);
        }

        console.log(`Estimated tokens: ${estimatedTokens}, Required credits: ${estimatedCredits}`);
      }

      // 使用请求中的 project 字段作为项目信息
      const prompt = this.generatePrompt(body);
      const response = await provider.generateContent(prompt);

      // 如果使用系统API，扣除实际使用的积分
      if (!body.useCustomApi && body.userId) {
        await this.updateUserCredits(body.userId, response.usage.total_tokens);
        // 使用系统API时只返回内容
        return {
          content: this.cleanAIResponse(response.content)
        };
      } else {
        // 使用自定义API时返回完整信息
        return {
          content: this.cleanAIResponse(response.content),
          usage: response.usage
        };
      }
    } catch (error) {
      console.error('Error in generateFormContent:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(
        HttpStatus.INTERNAL_SERVER_ERROR,
        `Failed to generate form content: ${error.message}`
      );
    }
  }
}