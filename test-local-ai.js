// Test script for local AI integration
// This script can be run in the browser console to test the new local AI processing

async function testLocalAIIntegration() {
  console.log('🧪 Testing Local AI Integration...');
  
  try {
    // Test 1: Check if background script is loaded
    console.log('📋 Test 1: Background script communication');
    const response = await new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({ action: 'getSettings' }, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(response);
        }
      });
    });
    console.log('✅ Background script communication successful');
    console.log('Settings:', response.settings);
    console.log('API Keys available:', Object.keys(response.apiKeys));
    
    // Test 2: Check storage structure
    console.log('\n📋 Test 2: Storage structure');
    const storage = await chrome.storage.sync.get([
      'formify_settings',
      'formify_api_keys',
      'formify_validated_keys'
    ]);
    
    const settings = storage.formify_settings || {};
    const apiKeys = storage.formify_api_keys || {};
    const validatedKeys = storage.formify_validated_keys || {};
    
    console.log('Settings structure:', {
      hasUseCustomApi: 'useCustomApi' in settings,
      defaultProvider: settings.defaultProvider,
      defaultModel: settings.defaultModel
    });
    
    if ('useCustomApi' in settings) {
      console.log('⚠️  Warning: useCustomApi still exists in settings');
    } else {
      console.log('✅ useCustomApi successfully removed from settings');
    }
    
    // Test 3: Test AI request (if API keys are available)
    console.log('\n📋 Test 3: AI request test');
    const provider = settings.defaultProvider || 'openai';
    
    if (validatedKeys[provider]) {
      console.log(`Testing AI request with ${provider}...`);
      
      const aiResponse = await new Promise((resolve, reject) => {
        chrome.runtime.sendMessage({
          type: 'aiRequest',
          prompt: 'Test prompt for local AI integration',
          options: {
            mode: 'general',
            description: 'Test description',
            formFields: [],
            language: 'en'
          }
        }, (response) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(response);
          }
        });
      });
      
      if (aiResponse.success) {
        console.log('✅ AI request successful');
        console.log('Response data:', aiResponse.data);
      } else {
        console.log('❌ AI request failed:', aiResponse.error);
      }
    } else {
      console.log(`⚠️  No validated API key for ${provider} - skipping AI request test`);
    }
    
    // Test 4: Check token statistics
    console.log('\n📋 Test 4: Token statistics');
    const tokenStats = await chrome.storage.sync.get('formify_token_stats');
    const stats = tokenStats.formify_token_stats || {};
    
    console.log('Token statistics available for providers:', Object.keys(stats));
    Object.entries(stats).forEach(([provider, data]) => {
      console.log(`${provider}:`, {
        totalTokens: data.totalTokens,
        lastUpdated: data.lastUpdated
      });
    });
    
    console.log('\n🎉 Local AI Integration Test Complete!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Function to test specific provider
async function testProvider(providerName) {
  console.log(`🧪 Testing ${providerName} provider...`);
  
  try {
    const storage = await chrome.storage.sync.get([
      'formify_settings',
      'formify_api_keys',
      'formify_validated_keys'
    ]);
    
    const settings = storage.formify_settings || {};
    const apiKeys = storage.formify_api_keys || {};
    const validatedKeys = storage.formify_validated_keys || {};
    
    if (!apiKeys[providerName]) {
      console.log(`❌ No API key found for ${providerName}`);
      return;
    }
    
    if (!validatedKeys[providerName]) {
      console.log(`❌ API key for ${providerName} is not validated`);
      return;
    }
    
    // Temporarily set this provider as default
    const originalProvider = settings.defaultProvider;
    settings.defaultProvider = providerName;
    await chrome.storage.sync.set({ formify_settings: settings });
    
    const response = await new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({
        type: 'aiRequest',
        prompt: `Test prompt for ${providerName}`,
        options: {
          mode: 'general',
          description: `Testing ${providerName} integration`,
          formFields: [],
          language: 'en'
        }
      }, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(response);
        }
      });
    });
    
    // Restore original provider
    settings.defaultProvider = originalProvider;
    await chrome.storage.sync.set({ formify_settings: settings });
    
    if (response.success) {
      console.log(`✅ ${providerName} test successful`);
      console.log('Response:', response.data);
    } else {
      console.log(`❌ ${providerName} test failed:`, response.error);
    }
    
  } catch (error) {
    console.error(`❌ ${providerName} test error:`, error);
  }
}

// Function to clean up old useCustomApi settings
async function cleanupOldSettings() {
  console.log('🧹 Cleaning up old useCustomApi settings...');
  
  const storage = await chrome.storage.sync.get('formify_settings');
  const settings = storage.formify_settings || {};
  
  if ('useCustomApi' in settings) {
    delete settings.useCustomApi;
    await chrome.storage.sync.set({ formify_settings: settings });
    console.log('✅ Removed useCustomApi from settings');
  } else {
    console.log('✅ No useCustomApi found in settings');
  }
}

// Export functions for console use
window.testLocalAI = {
  testAll: testLocalAIIntegration,
  testProvider: testProvider,
  cleanup: cleanupOldSettings
};

console.log('🚀 Local AI Test Functions Loaded!');
console.log('Usage:');
console.log('  testLocalAI.testAll() - Run all tests');
console.log('  testLocalAI.testProvider("openai") - Test specific provider');
console.log('  testLocalAI.cleanup() - Clean up old settings');
