# Local AI Integration Testing Guide

## Overview
This guide provides step-by-step instructions for testing the new local AI integration in Fillify.

## Pre-Testing Setup

### 1. Build the Extension
```bash
npm run build
```

### 2. Load Extension in Browser
1. Open Chrome/Firefox extension management page
2. Enable "Developer mode"
3. Load unpacked extension from `.output` directory

### 3. Prepare Test API Keys
Ensure you have valid API keys for the providers you want to test:
- OpenAI API key
- Claude API key  
- Moonshot API key
- Gemini API key
- DeepSeek API key
- OpenRouter API key
- Ollama running locally (if testing)

## Testing Procedure

### Step 1: Configure API Keys
1. Open Fillify settings page
2. Add API keys for each provider you want to test
3. Verify each key shows as "validated" (green checkmark)

### Step 2: Test Form Detection
1. Navigate to a test page with forms:
   - Gmail compose
   - GitHub new issue
   - Any contact form
2. Open Fillify popup
3. Verify form fields are detected (no error messages)

### Step 3: Test Each Provider

#### OpenAI Testing
1. Set OpenAI as default provider in settings
2. Choose a GPT model (e.g., gpt-3.5-turbo)
3. Test form filling with different modes:
   - General mode: "Fill a contact form for <PERSON>"
   - Email mode: "Write a professional inquiry email"
   - Bug Report mode: "Login button not working"

#### Claude Testing
1. Switch to Claude provider
2. Select Claude model (e.g., claude-3-sonnet-20240229)
3. Test same scenarios as OpenAI
4. Verify responses are generated correctly

#### Moonshot Testing
1. Switch to Moonshot provider
2. Test with Chinese content: "写一封中文邮件"
3. Verify Chinese language support

#### Gemini Testing
1. Switch to Gemini provider
2. Test with free models (Flash variants)
3. Verify responses and token tracking

#### DeepSeek Testing
1. Switch to DeepSeek provider
2. Test reasoning tasks
3. Verify API integration

#### OpenRouter Testing
1. Switch to OpenRouter provider
2. Test different model routing
3. Verify multiple model support

#### Ollama Testing (if available)
1. Ensure Ollama is running locally
2. Switch to Ollama provider
3. Test local model processing
4. Verify no external network calls

### Step 4: Verify Token Tracking
1. Open settings page
2. Navigate to "Token Usage" section
3. Verify statistics are updated after each request
4. Check different providers show separate statistics

### Step 5: Error Handling Testing
1. Test with invalid API key
2. Test with network disconnection
3. Test with unsupported models
4. Verify error messages are clear and helpful

## Expected Results

### Successful Test Indicators
- ✅ Form fields detected correctly
- ✅ AI responses generated for all providers
- ✅ Token usage statistics updated
- ✅ No backend server calls (check network tab)
- ✅ Error handling works properly

### Performance Expectations
- **Response Time**: Should be faster than before (no proxy server)
- **Token Accuracy**: Usage statistics should match provider dashboards
- **Error Messages**: Should be specific to each provider

## Debugging

### Browser Console Logs
Check browser console for:
- AI request logs
- Token usage updates
- Error messages
- Network request details

### Network Tab Verification
Verify direct API calls to:
- `api.openai.com` (OpenAI)
- `api.anthropic.com` (Claude)
- `api.moonshot.cn` (Moonshot)
- `generativelanguage.googleapis.com` (Gemini)
- `api.deepseek.com` (DeepSeek)
- `openrouter.ai` (OpenRouter)
- `localhost:11434` (Ollama)

### Common Issues and Solutions

#### Issue: API Key Validation Fails
**Solution**: 
- Check API key format
- Verify account has sufficient credits
- Test key directly with provider's API

#### Issue: CORS Errors
**Solution**:
- Verify extension permissions in manifest
- Check host_permissions include provider domains
- Reload extension after changes

#### Issue: Token Statistics Not Updating
**Solution**:
- Check browser console for errors
- Verify usage data structure in response
- Test with different providers

#### Issue: Form Fields Not Detected
**Solution**:
- Refresh the target page
- Check content script injection
- Verify page compatibility

## Rollback Plan

If issues are found, you can temporarily rollback by:

1. **Restore Server Processing**: Uncomment server-based code in `handleAiRequest()`
2. **Re-enable useCustomApi**: Add back the setting in interfaces
3. **Update Settings UI**: Restore server/local toggle option

## Performance Monitoring

### Metrics to Track
- **Response Time**: Compare before/after implementation
- **Success Rate**: Monitor failed requests per provider
- **Token Accuracy**: Compare with provider dashboards
- **User Feedback**: Monitor for any reported issues

### Monitoring Tools
- Browser DevTools Network tab
- Extension console logs
- Provider API dashboards
- User feedback channels

## Next Steps

After successful testing:
1. Update user documentation
2. Announce privacy improvements
3. Monitor performance metrics
4. Consider removing deprecated backend endpoints
5. Plan future enhancements (caching, retry logic, etc.)
