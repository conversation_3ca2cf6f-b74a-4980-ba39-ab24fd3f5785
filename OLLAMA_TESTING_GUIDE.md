# Ollama Testing Guide for Fillify

## Prerequisites

### 1. Install Ollama
```bash
# macOS
brew install ollama

# Or download from https://ollama.com/download
```

### 2. Start Ollama Service
```bash
# Start Ollama (usually runs automatically after installation)
ollama serve
```

### 3. Pull a Model
```bash
# Pull a lightweight model for testing
ollama pull llama2

# Or other models
ollama pull mistral
ollama pull phi
```

### 4. Verify Ollama is Running
```bash
# Check if Ollama is accessible
curl http://localhost:11434/v1/models

# Should return a JSON response with available models
```

## Testing Steps

### 1. Load the Extension
1. Open Chrome and go to `chrome://extensions/`
2. Enable "Developer mode"
3. Click "Load unpacked" and select the `.output/chrome-mv3` folder
4. Verify the extension loads without errors

### 2. Configure Ollama Provider
1. Click the Fillify extension icon
2. Go to Settings (or complete onboarding if first time)
3. Find the "Ollama" provider section
4. Enter endpoint: `http://localhost:11434` (default)
5. Click "Validate" - should show success message

### 3. Set as Default Provider
1. In the "Default Provider" dropdown, select "Ollama"
2. In the "Default Model" dropdown, select an available model (e.g., "llama2")
3. Save settings

### 4. Test Form Filling
1. Go to any website with a form (e.g., GitHub issue creation)
2. Click the Fillify extension icon
3. Enter a description like "Create a bug report for login issues"
4. Click "Fill Form"
5. Verify the form gets filled with AI-generated content

## Troubleshooting

### Common Issues

#### 1. "Cannot connect to Ollama server"
**Solutions**:
- Verify Ollama is running: `ollama serve`
- Check if port 11434 is accessible: `curl http://localhost:11434/v1/models`
- Try alternative endpoint: `http://127.0.0.1:11434`

#### 2. CORS Errors in Console
**Solutions**:
- Ensure the extension has been rebuilt with CORS fixes
- Check that `rules.json` exists in the extension folder
- Verify `declarativeNetRequest` permission is granted

#### 3. "Model not found" Error
**Solutions**:
- List available models: `ollama list`
- Pull the required model: `ollama pull llama2`
- Refresh the model list in extension settings

#### 4. Slow Response Times
**Solutions**:
- Use smaller models (phi, mistral) for faster responses
- Ensure sufficient system resources (RAM, CPU)
- Check Ollama logs: `ollama logs`

### Debug Information

#### Check Extension Console
1. Go to `chrome://extensions/`
2. Click "Details" on Fillify extension
3. Click "Inspect views: background page"
4. Check console for error messages

#### Check Network Requests
1. Open browser DevTools (F12)
2. Go to Network tab
3. Trigger a form fill operation
4. Look for requests to `localhost:11434`

#### Verify CORS Rules
1. In DevTools Network tab, check request headers
2. Verify Origin header is removed for localhost requests
3. Check that requests reach Ollama server

## Expected Behavior

### Successful Integration
- ✅ Ollama appears in provider list
- ✅ Connection validation succeeds
- ✅ Models are fetched and displayed
- ✅ Form filling works with local AI
- ✅ No requests sent to external servers
- ✅ Fast response times (local processing)

### Performance Expectations
- **Connection**: < 100ms
- **Model Loading**: 1-5 seconds (first request)
- **Generation**: 2-10 seconds (depending on model and prompt)
- **Form Filling**: Near-instant after generation

## Advanced Testing

### Test Different Models
```bash
# Test with different model sizes
ollama pull llama2:7b
ollama pull llama2:13b
ollama pull mistral:7b
ollama pull phi:2.7b
```

### Test Custom Endpoints
- Try different ports: `http://localhost:11435`
- Test with IP address: `http://127.0.0.1:11434`
- Test with custom Ollama configurations

### Test Various Form Types
- Bug report forms (GitHub, Jira)
- Contact forms
- Email composition
- Survey forms
- Registration forms

## Success Criteria

The Ollama integration is working correctly when:

1. **Connection**: Extension can connect to local Ollama server
2. **Authentication**: No API key required, connection works with default settings
3. **Model Management**: Can fetch and display available models
4. **Form Filling**: Successfully generates and fills form content
5. **Privacy**: No external network requests for Ollama operations
6. **Performance**: Reasonable response times for local processing
7. **Error Handling**: Clear error messages for common issues

## Notes

- Ollama models run entirely on your local machine
- No data is sent to external servers when using Ollama
- First request may be slower due to model loading
- Larger models provide better quality but slower responses
- Extension works offline once Ollama is set up
