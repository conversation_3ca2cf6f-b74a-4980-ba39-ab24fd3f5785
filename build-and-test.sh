#!/bin/bash

# Fillify Local AI Integration - Build and Test Script
# This script builds the extension and provides testing instructions

echo "🚀 Fillify Local AI Integration - Build and Test"
echo "================================================"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Please run this script from the project root."
    exit 1
fi

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

echo "🔨 Building extension..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    echo ""
    echo "📋 Next Steps:"
    echo "1. Open Chrome/Firefox and go to extensions page"
    echo "2. Enable 'Developer mode'"
    echo "3. Click 'Load unpacked' and select the '.output' directory"
    echo ""
    echo "🧪 Testing Instructions:"
    echo "1. Open the extension settings and add your API keys"
    echo "2. Navigate to a form page (Gmail, GitHub issues, etc.)"
    echo "3. Click the Fillify extension icon"
    echo "4. Test form filling with different providers"
    echo ""
    echo "🔍 Debug Tools:"
    echo "1. Open browser console and load test-local-ai.js"
    echo "2. Run: testLocalAI.testAll()"
    echo "3. Check Network tab to verify no backend server calls"
    echo ""
    echo "📊 Verify Local Processing:"
    echo "- All API calls should go directly to AI providers"
    echo "- No calls to fillify-343190162770.asia-east1.run.app"
    echo "- Token statistics should update correctly"
    echo ""
    echo "🎉 Local AI Integration is ready for testing!"
else
    echo "❌ Build failed. Please check the error messages above."
    exit 1
fi
